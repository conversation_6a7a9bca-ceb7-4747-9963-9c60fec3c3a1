#property strict

//+------------------------------------------------------------------+
//| TestTradingPipelineDriverLoggerDecorator.mqh                    |
//| TradingPipelineDriverLoggerDecorator 單元測試                   |
//| 測試驅動器日誌裝飾者的裝飾者模式和日誌記錄功能                   |
//+------------------------------------------------------------------+

#include "../TestFramework.mqh"
#include "../../feature/TradingPipelineDriverLoggerDecorator.mqh"
#include "../../../MQL4Logger/FileLog.mqh"

//+------------------------------------------------------------------+
//| 模擬日誌記錄器類                                                 |
//+------------------------------------------------------------------+
class MockFileLogForDriverDecorator : public CFileLog
{
private:
    string m_lastMessage;
    string m_lastLevel;
    int m_messageCount;

public:
    MockFileLogForDriverDecorator() : CFileLog("test.log", WARNING, false, false), m_lastMessage(""), m_lastLevel(""), m_messageCount(0) {}
    virtual ~MockFileLogForDriverDecorator() {}

    virtual void Info(const string message)
    {
        m_lastMessage = message;
        m_lastLevel = "INFO";
        m_messageCount++;
    }

    virtual void Debug(const string message)
    {
        m_lastMessage = message;
        m_lastLevel = "DEBUG";
        m_messageCount++;
    }

    virtual void Warning(const string message)
    {
        m_lastMessage = message;
        m_lastLevel = "WARNING";
        m_messageCount++;
    }

    virtual void Error(const string message)
    {
        m_lastMessage = message;
        m_lastLevel = "ERROR";
        m_messageCount++;
    }

    // 測試輔助方法
    string GetLastMessage() const { return m_lastMessage; }
    string GetLastLevel() const { return m_lastLevel; }
    int GetMessageCount() const { return m_messageCount; }
    void Reset() { m_lastMessage = ""; m_lastLevel = ""; m_messageCount = 0; }
};

//+------------------------------------------------------------------+
//| TradingPipelineDriverLoggerDecorator 測試類                     |
//+------------------------------------------------------------------+
class TestTradingPipelineDriverLoggerDecorator : public TestCase
{
private:
    TestRunner* m_runner;

public:
    // 構造函數
    TestTradingPipelineDriverLoggerDecorator(TestRunner* runner = NULL)
        : TestCase("TestTradingPipelineDriverLoggerDecorator"), m_runner(runner) {}

    // 析構函數
    virtual ~TestTradingPipelineDriverLoggerDecorator() {}

    // 運行所有測試
    virtual void RunTests() override
    {
        Print("=== 開始執行 TradingPipelineDriverLoggerDecorator 單元測試 ===");

        TestConstructor();
        TestBasicProperties();
        TestDecoratorPattern();
        TestLoggingFunctionality();
        TestInitializationWithLogging();
        TestDetailedLoggingControl();
        TestComponentAccess();

        Print("=== TradingPipelineDriverLoggerDecorator 單元測試完成 ===");
    }

private:
    // 測試構造函數
    void TestConstructor()
    {
        Print("--- 測試 TradingPipelineDriverLoggerDecorator 構造函數 ---");

        MockFileLogForDriverDecorator* logger = new MockFileLogForDriverDecorator();
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineDriverLoggerDecorator* decorator = new TradingPipelineDriverLoggerDecorator(driver, logger, true);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestConstructor - 實例創建",
                decorator != NULL,
                decorator != NULL ? "裝飾者實例創建成功" : "裝飾者實例創建失敗"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestConstructor - 名稱設置",
                decorator.GetName() == "TradingPipelineDriverLoggerDecorator",
                "驅動器名稱設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestConstructor - 類型設置",
                decorator.GetType() == "DriverLoggerDecorator",
                "驅動器類型設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestConstructor - 日誌器設置",
                decorator.GetLogger() == logger,
                "日誌器設置正確"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestConstructor - 詳細日誌啟用",
                decorator.IsDetailedLoggingEnabled(),
                "詳細日誌記錄已啟用"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestConstructor - 被裝飾驅動器",
                decorator.GetWrappedDriver() != NULL,
                "被裝飾的驅動器實例獲取成功"
            ));
        }

        delete decorator;
        delete logger;
    }

    // 測試基本屬性
    void TestBasicProperties()
    {
        Print("--- 測試 TradingPipelineDriverLoggerDecorator 基本屬性 ---");

        MockFileLogForDriverDecorator* logger = new MockFileLogForDriverDecorator();
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineDriverLoggerDecorator* decorator = new TradingPipelineDriverLoggerDecorator(driver, logger, false);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestBasicProperties - 詳細日誌禁用",
                !decorator.IsDetailedLoggingEnabled(),
                "詳細日誌記錄已禁用"
            ));

            // 測試初始化前的狀態
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestBasicProperties - 初始化狀態",
                !decorator.IsInitialized(),
                "初始狀態為未初始化"
            ));
        }

        delete decorator;
        delete logger;
    }

    // 測試裝飾者模式
    void TestDecoratorPattern()
    {
        Print("--- 測試 TradingPipelineDriverLoggerDecorator 裝飾者模式 ---");

        MockFileLogForDriverDecorator* logger = new MockFileLogForDriverDecorator();
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineDriverLoggerDecorator* decorator = new TradingPipelineDriverLoggerDecorator(driver, logger, true);

        TradingPipelineDriver* wrappedDriver = decorator.GetWrappedDriver();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestDecoratorPattern - 被裝飾驅動器不為空",
                wrappedDriver != NULL,
                "被裝飾的驅動器實例存在"
            ));

            // 測試被裝飾驅動器的狀態
            if(wrappedDriver != NULL)
            {
                m_runner.RecordResult(new TestResult(
                    "TestTradingPipelineDriverLoggerDecorator::TestDecoratorPattern - 被裝飾驅動器已初始化",
                    wrappedDriver.IsInitialized(),
                    "被裝飾的驅動器已初始化"
                ));
            }
        }

        delete decorator;
        delete logger;
    }

    // 測試日誌記錄功能
    void TestLoggingFunctionality()
    {
        Print("--- 測試 TradingPipelineDriverLoggerDecorator 日誌記錄功能 ---");

        MockFileLogForDriverDecorator* logger = new MockFileLogForDriverDecorator();
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineDriverLoggerDecorator* decorator = new TradingPipelineDriverLoggerDecorator(driver, logger, true);

        // 重置日誌計數
        logger.Reset();

        // 測試詳細日誌控制
        decorator.SetDetailedLogging(false);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestLoggingFunctionality - 詳細日誌控制",
                !decorator.IsDetailedLoggingEnabled(),
                "詳細日誌控制功能正常"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestLoggingFunctionality - 日誌記錄觸發",
                logger.GetMessageCount() > 0,
                "設置詳細日誌時觸發了日誌記錄"
            ));
        }

        delete decorator;
        delete logger;
    }

    // 測試帶日誌的初始化
    void TestInitializationWithLogging()
    {
        Print("--- 測試 TradingPipelineDriverLoggerDecorator 帶日誌的初始化 ---");

        MockFileLogForDriverDecorator* logger = new MockFileLogForDriverDecorator();
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineDriverLoggerDecorator* decorator = new TradingPipelineDriverLoggerDecorator(driver, logger, true);

        logger.Reset();

        // 執行初始化（應該觸發日誌記錄）
        bool initResult = decorator.Initialize();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestInitializationWithLogging - 初始化成功",
                initResult && decorator.IsInitialized(),
                "初始化成功"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestInitializationWithLogging - 日誌記錄觸發",
                logger.GetMessageCount() > 0,
                "初始化過程觸發了日誌記錄"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestInitializationWithLogging - 日誌內容檢查",
                StringFind(logger.GetLastMessage(), "初始化") >= 0 || StringFind(logger.GetLastMessage(), "驅動器") >= 0,
                "日誌內容包含初始化相關信息"
            ));
        }

        delete decorator;
        delete logger;
    }

    // 測試詳細日誌控制
    void TestDetailedLoggingControl()
    {
        Print("--- 測試 TradingPipelineDriverLoggerDecorator 詳細日誌控制 ---");

        MockFileLogForDriverDecorator* logger = new MockFileLogForDriverDecorator();
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineDriverLoggerDecorator* decorator = new TradingPipelineDriverLoggerDecorator(driver, logger, false);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestDetailedLoggingControl - 初始詳細日誌狀態",
                !decorator.IsDetailedLoggingEnabled(),
                "初始詳細日誌記錄已禁用"
            ));
        }

        // 啟用詳細日誌
        decorator.SetDetailedLogging(true);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestDetailedLoggingControl - 啟用詳細日誌",
                decorator.IsDetailedLoggingEnabled(),
                "詳細日誌記錄已啟用"
            ));
        }

        // 禁用詳細日誌
        decorator.SetDetailedLogging(false);

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestDetailedLoggingControl - 禁用詳細日誌",
                !decorator.IsDetailedLoggingEnabled(),
                "詳細日誌記錄已禁用"
            ));
        }

        delete decorator;
        delete logger;
    }

    // 測試組件訪問
    void TestComponentAccess()
    {
        Print("--- 測試 TradingPipelineDriverLoggerDecorator 組件訪問 ---");

        MockFileLogForDriverDecorator* logger = new MockFileLogForDriverDecorator();
        TradingPipelineDriver* driver = TradingPipelineDriver::GetInstance();
        TradingPipelineDriverLoggerDecorator* decorator = new TradingPipelineDriverLoggerDecorator(driver, logger, true);

        // 確保初始化完成
        decorator.Initialize();

        if(m_runner != NULL)
        {
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestComponentAccess - 管理器訪問",
                decorator.GetManager() != NULL,
                "管理器訪問正常"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestComponentAccess - 註冊器訪問",
                decorator.GetRegistry() != NULL,
                "註冊器訪問正常"
            ));

            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestComponentAccess - 探索器訪問",
                decorator.GetExplorer() != NULL,
                "探索器訪問正常"
            ));

            // 測試默認配置設置
            bool configResult = decorator.SetupDefaultConfiguration();
            m_runner.RecordResult(new TestResult(
                "TestTradingPipelineDriverLoggerDecorator::TestComponentAccess - 默認配置設置",
                configResult,
                "默認配置設置成功"
            ));
        }

        delete decorator;
        delete logger;
    }
};
