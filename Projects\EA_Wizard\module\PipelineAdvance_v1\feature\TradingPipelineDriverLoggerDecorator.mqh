#property strict

//+------------------------------------------------------------------+
//| TradingPipelineDriverLoggerDecorator.mqh                        |
//| 交易流水線驅動器日誌裝飾者                                       |
//| 為 TradingPipelineDriver 添加詳細的日誌記錄功能                 |
//+------------------------------------------------------------------+

#include "../TradingPipelineDriverBase.mqh"
#include "../TradingPipelineDriver.mqh"
#include "../../MQL4Logger/FileLog.mqh"

//+------------------------------------------------------------------+
//| 日誌記錄器常量配置                                               |
//+------------------------------------------------------------------+
#ifdef _DEBUG
// 調試模式下的日誌設置
#define DRIVER_LOGGER_DEFAULT_FILE_NAME "TradingPipelineDriver_Debug.log"
#define DRIVER_LOGGER_DEFAULT_LEVEL DEBUG
#define DRIVER_LOGGER_DEFAULT_PRINT_TO_CONSOLE true
#define DRIVER_LOGGER_DEFAULT_APPEND_TO_EXISTING false
#else
// 正常模式下的日誌設置
#define DRIVER_LOGGER_DEFAULT_FILE_NAME "TradingPipelineDriver.log"
#define DRIVER_LOGGER_DEFAULT_LEVEL INFO
#define DRIVER_LOGGER_DEFAULT_PRINT_TO_CONSOLE false
#define DRIVER_LOGGER_DEFAULT_APPEND_TO_EXISTING true
#endif

//+------------------------------------------------------------------+
//| 交易流水線驅動器日誌裝飾者                                       |
//| 實現裝飾者模式，為驅動器添加詳細的日誌記錄功能                   |
//| 記錄初始化過程、組件管理和配置設置                               |
//+------------------------------------------------------------------+
class TradingPipelineDriverLoggerDecorator : public TradingPipelineDriverBase
{
private:
    TradingPipelineDriver* m_wrappedDriver;     // 被裝飾的驅動器實例
    CFileLog* m_logger;                         // 日誌記錄器實例
    bool m_enableDetailedLogging;               // 是否啟用詳細日誌記錄

public:
    //+------------------------------------------------------------------+
    //| 構造函數                                                         |
    //| @param wrappedDriver 被裝飾的驅動器實例（必須傳入）              |
    //| @param logger 日誌記錄器（必須傳入）                             |
    //| @param enableDetailedLogging 是否啟用詳細日誌記錄               |
    //+------------------------------------------------------------------+
    TradingPipelineDriverLoggerDecorator(TradingPipelineDriver* wrappedDriver,
                                        CFileLog* logger,
                                        bool enableDetailedLogging = true)
        : TradingPipelineDriverBase("TradingPipelineDriverLoggerDecorator", "DriverLoggerDecorator"),
          m_wrappedDriver(wrappedDriver),
          m_logger(logger),
          m_enableDetailedLogging(enableDetailedLogging)
    {
        LogConstructor();

        // 驗證傳入的驅動器實例
        if(m_wrappedDriver == NULL)
        {
            LogError("傳入的驅動器實例為空");
            return;
        }

        // 複製被裝飾驅動器的組件引用
        m_manager = m_wrappedDriver.GetManager();
        m_registry = m_wrappedDriver.GetRegistry();
        m_explorer = m_wrappedDriver.GetExplorer();
        m_isInitialized = m_wrappedDriver.IsInitialized();

        LogDriverWrapped();
    }

    //+------------------------------------------------------------------+
    //| 析構函數                                                         |
    //+------------------------------------------------------------------+
    virtual ~TradingPipelineDriverLoggerDecorator()
    {
        LogDestructor();
        // 注意：日誌記錄器由外部管理，不在此處刪除
    }

    //+------------------------------------------------------------------+
    //| 覆寫 ITradingPipelineDriver 介面方法以添加日誌記錄               |
    //+------------------------------------------------------------------+

    //+------------------------------------------------------------------+
    //| 初始化驅動器 - 添加日誌記錄                                     |
    //+------------------------------------------------------------------+
    virtual bool Initialize() override
    {
        LogInfo("開始初始化交易流水線驅動器");

        if(m_wrappedDriver == NULL)
        {
            LogError("被裝飾的驅動器實例為空，無法初始化");
            SetResult(false, "被裝飾的驅動器實例為空", ERROR_LEVEL_ERROR);
            return false;
        }

        // 驅動器已經在單例模式下自動初始化
        bool result = m_wrappedDriver.IsInitialized();

        if(result)
        {
            // 同步狀態
            m_manager = m_wrappedDriver.GetManager();
            m_registry = m_wrappedDriver.GetRegistry();
            m_explorer = m_wrappedDriver.GetExplorer();
            m_isInitialized = true;

            LogInfo("驅動器初始化成功");
            LogComponentStatus();
            SetResult(true, "驅動器初始化成功", ERROR_LEVEL_INFO);
        }
        else
        {
            LogError("驅動器初始化失敗");
            SetResult(false, "驅動器初始化失敗", ERROR_LEVEL_ERROR);
        }

        return result;
    }

    //+------------------------------------------------------------------+
    //| 獲取容器管理器 - 添加日誌記錄                                   |
    //+------------------------------------------------------------------+
    virtual TradingPipelineContainerManager* GetManager() const override
    {
        if(m_enableDetailedLogging)
        {
            LogDebug("獲取容器管理器: " + (m_manager != NULL ? "成功" : "失敗"));
        }
        return m_manager;
    }

    //+------------------------------------------------------------------+
    //| 獲取註冊器 - 添加日誌記錄                                       |
    //+------------------------------------------------------------------+
    virtual TradingPipelineRegistry* GetRegistry() const override
    {
        if(m_enableDetailedLogging)
        {
            LogDebug("獲取註冊器: " + (m_registry != NULL ? "成功" : "失敗"));
        }
        return m_registry;
    }

    //+------------------------------------------------------------------+
    //| 獲取探索器 - 添加日誌記錄                                       |
    //+------------------------------------------------------------------+
    virtual TradingPipelineExplorer* GetExplorer() const override
    {
        if(m_enableDetailedLogging)
        {
            LogDebug("獲取探索器: " + (m_explorer != NULL ? "成功" : "失敗"));
        }
        return m_explorer;
    }

    //+------------------------------------------------------------------+
    //| 檢查初始化狀態 - 添加日誌記錄                                   |
    //+------------------------------------------------------------------+
    virtual bool IsInitialized() const override
    {
        bool result = TradingPipelineDriverBase::IsInitialized();
        if(m_enableDetailedLogging)
        {
            LogDebug("檢查初始化狀態: " + (result ? "已初始化" : "未初始化"));
        }
        return result;
    }

    //+------------------------------------------------------------------+
    //| 設置默認配置 - 添加日誌記錄                                     |
    //+------------------------------------------------------------------+
    virtual bool SetupDefaultConfiguration() override
    {
        LogInfo("開始設置默認配置");

        if(m_wrappedDriver == NULL)
        {
            LogError("被裝飾的驅動器實例為空，無法設置配置");
            return false;
        }

        bool result = m_wrappedDriver.SetupDefaultConfiguration();

        if(result)
        {
            LogInfo("默認配置設置成功");
            if(m_enableDetailedLogging)
            {
                LogConfigurationDetails();
            }
        }
        else
        {
            LogError("默認配置設置失敗");
        }

        return result;
    }

    //+------------------------------------------------------------------+
    //| 清理驅動器 - 添加日誌記錄                                       |
    //+------------------------------------------------------------------+
    virtual void Cleanup() override
    {
        LogInfo("開始清理驅動器資源");

        if(m_wrappedDriver != NULL)
        {
            m_wrappedDriver.Cleanup();
            LogInfo("被裝飾驅動器清理完成");
        }

        // 清理自身狀態
        m_manager = NULL;
        m_registry = NULL;
        m_explorer = NULL;
        m_isInitialized = false;

        LogInfo("驅動器清理完成");
    }

    //+------------------------------------------------------------------+
    //| 獲取驅動器名稱 - 添加裝飾者標識                                 |
    //+------------------------------------------------------------------+
    virtual string GetName() const override
    {
        return "[Logger]" + TradingPipelineDriverBase::GetName();
    }

protected:
    //+------------------------------------------------------------------+
    //| 實現抽象方法 - 初始化核心組件                                   |
    //+------------------------------------------------------------------+
    virtual bool InitializeComponents() override
    {
        // 裝飾者模式：委託給被裝飾的驅動器
        if(m_wrappedDriver == NULL)
        {
            LogError("被裝飾的驅動器實例為空，無法初始化組件");
            return false;
        }

        LogInfo("委託組件初始化給被裝飾的驅動器");
        return m_wrappedDriver.IsInitialized();
    }

    //+------------------------------------------------------------------+
    //| 實現抽象方法 - 設置配置                                         |
    //+------------------------------------------------------------------+
    virtual bool SetupConfiguration() override
    {
        // 裝飾者模式：委託給被裝飾的驅動器
        if(m_wrappedDriver == NULL)
        {
            LogError("被裝飾的驅動器實例為空，無法設置配置");
            return false;
        }

        LogInfo("委託配置設置給被裝飾的驅動器");
        return m_wrappedDriver.SetupDefaultConfiguration();
    }

private:
    //+------------------------------------------------------------------+
    //| 私有日誌記錄方法                                                 |
    //+------------------------------------------------------------------+

    void LogConstructor()
    {
        LogInfo("創建交易流水線驅動器日誌裝飾者");
    }

    void LogDestructor()
    {
        LogInfo("銷毀交易流水線驅動器日誌裝飾者");
    }

    void LogDriverWrapped()
    {
        LogInfo("成功包裝 TradingPipelineDriver 實例");
        if(m_enableDetailedLogging)
        {
            LogDebug("被裝飾驅動器狀態: " + (m_wrappedDriver.IsInitialized() ? "已初始化" : "未初始化"));
        }
    }

    void LogComponentStatus()
    {
        if(m_enableDetailedLogging)
        {
            LogDebug("組件狀態檢查:");
            LogDebug("  - 容器管理器: " + (m_manager != NULL ? "已創建" : "未創建"));
            LogDebug("  - 註冊器: " + (m_registry != NULL ? "已創建" : "未創建"));
            LogDebug("  - 探索器: " + (m_explorer != NULL ? "已創建" : "未創建"));
        }
    }

    void LogConfigurationDetails()
    {
        if(m_registry != NULL)
        {
            LogDebug("配置詳情:");
            LogDebug("  - 總註冊數量: " + IntegerToString(m_registry.GetTotalRegistrations()));
        }
    }

    void LogInfo(string message)
    {
        if(m_logger != NULL)
        {
            string logMessage = StringFormat("[%s] %s", GetName(), message);
            m_logger.Info(logMessage);
        }
    }

    void LogDebug(string message) const
    {
        if(m_logger != NULL && m_enableDetailedLogging)
        {
            string logMessage = StringFormat("[%s] %s", GetName(), message);
            m_logger.Debug(logMessage);
        }
    }

    void LogError(string message)
    {
        if(m_logger != NULL)
        {
            string logMessage = StringFormat("[%s] ERROR: %s", GetName(), message);
            m_logger.Error(logMessage);
        }
    }

public:
    //+------------------------------------------------------------------+
    //| 日誌配置方法                                                     |
    //+------------------------------------------------------------------+

    void SetDetailedLogging(bool enabled)
    {
        m_enableDetailedLogging = enabled;
        LogInfo("詳細日誌記錄已" + (enabled ? "啟用" : "禁用"));
    }

    CFileLog* GetLogger() const
    {
        return m_logger;
    }

    bool IsDetailedLoggingEnabled() const
    {
        return m_enableDetailedLogging;
    }

    TradingPipelineDriver* GetWrappedDriver() const
    {
        return m_wrappedDriver;
    }
};
